"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Euro, Minus, Plus, Users } from "lucide-react";
import { useEffect, useState } from "react";

interface PricingTier {
	id: string;
	tier_name: string;
	price: number;
	min_age: number;
	max_age: number | null;
}

interface Participant {
	id: string;
	age: number;
	tier: PricingTier;
	price: number;
}

interface TierCount {
	tier: PricingTier;
	count: number;
}

interface PricingCalculatorProps {
	pricingTiers: PricingTier[];
	maxParticipants: number;
	onPriceChange: (totalPrice: number, participants: Participant[]) => void;
	className?: string;
	basePrice?: number;
	fixedPrice?: boolean; // If true, price doesn't change with participant count
	initialParticipants?: Participant[]; // Initial participants to populate the calculator
}

export function PricingCalculator({
	pricingTiers,
	maxParticipants,
	onPriceChange,
	className = "",
	basePrice = 0,
	fixedPrice = false,
	initialParticipants = [],
}: PricingCalculatorProps) {
	const [tierCounts, setTierCounts] = useState<TierCount[]>([]);
	const [isInitialized, setIsInitialized] = useState(false);

	// Initialize tier counts
	useEffect(() => {
		if (pricingTiers.length > 0 && !isInitialized) {
			const initialCounts = pricingTiers.map((tier) => ({
				tier,
				count: 0,
			}));

			// If we have initial participants, use them to set counts
			if (initialParticipants.length > 0) {
				initialParticipants.forEach((participant) => {
					// Try to match by tier ID first, then by age
					let matchingTier = null;
					let matchingIndex = -1;

					if (participant.tier) {
						matchingIndex = initialCounts.findIndex((tc) => tc.tier.id === participant.tier!.id);
						if (matchingIndex !== -1) {
							matchingTier = participant.tier;
						}
					}

					// If tier ID doesn't match or no tier info, try to match by age
					if (matchingIndex === -1) {
						matchingTier = pricingTiers.find(
							(tier) =>
								participant.age >= tier.min_age &&
								(tier.max_age === null || participant.age <= tier.max_age)
						);
						if (matchingTier) {
							matchingIndex = initialCounts.findIndex((tc: TierCount) => tc.tier.id === matchingTier!.id);
						}
					}

					// Add participant to the matching tier
					if (matchingIndex !== -1) {
						initialCounts[matchingIndex].count += 1;
					}
				});
			} else {
				// Start with one adult participant (default behavior)
				const adultTier =
					pricingTiers.find((tier) => tier.tier_name.toLowerCase().includes("adult") || tier.min_age >= 18) ||
					pricingTiers[0];

				const adultIndex = initialCounts.findIndex((tc) => tc.tier.id === adultTier.id);
				if (adultIndex !== -1) {
					initialCounts[adultIndex].count = 1;
				}
			}

			setTierCounts(initialCounts);
			setIsInitialized(true);
		} else if (pricingTiers.length === 0 && !isInitialized) {
			// Create a default tier if no pricing tiers exist
			const defaultTier: PricingTier = {
				id: "default",
				tier_name: "Participant",
				price: basePrice,
				min_age: 0,
				max_age: null,
			};

			setTierCounts([
				{
					tier: defaultTier,
					count: initialParticipants.length > 0 ? initialParticipants.length : 1,
				},
			]);
			setIsInitialized(true);
		}
	}, [pricingTiers, initialParticipants, basePrice, isInitialized]);

	// Update parent component when counts change
	useEffect(() => {
		const participants: Participant[] = [];
		let participantId = 1;

		if (fixedPrice) {
			// For fixed pricing, create participants but only the first one has the price
			const totalParticipants = tierCounts.reduce((sum, tc) => sum + tc.count, 0);
			let isFirstParticipant = true;

			tierCounts.forEach(({ tier, count }) => {
				for (let i = 0; i < count; i++) {
					participants.push({
						id: `participant-${participantId++}`,
						age: tier.min_age,
						tier,
						price: isFirstParticipant ? basePrice : 0, // Only first participant gets the price
					});
					isFirstParticipant = false;
				}
			});

			// Fixed price regardless of participant count
			onPriceChange(basePrice, participants);
		} else {
			// Normal per-participant pricing
			tierCounts.forEach(({ tier, count }) => {
				for (let i = 0; i < count; i++) {
					participants.push({
						id: `participant-${participantId++}`,
						age: tier.min_age,
						tier,
						price: tier.price,
					});
				}
			});

			const totalPrice = participants.reduce((sum, p) => sum + p.price, 0);
			onPriceChange(totalPrice, participants);
		}
	}, [tierCounts, fixedPrice, basePrice]);

	const updateTierCount = (tierId: string, delta: number) => {
		setTierCounts((prev) => {
			const newCounts = prev.map((tc) => {
				if (tc.tier.id === tierId) {
					const newCount = Math.max(0, tc.count + delta);
					return { ...tc, count: newCount };
				}
				return tc;
			});

			// Check total participants limit
			const totalParticipants = newCounts.reduce((sum, tc) => sum + tc.count, 0);
			if (totalParticipants > maxParticipants) {
				return prev; // Don't update if it exceeds limit
			}

			return newCounts;
		});
	};

	const getTierDisplayName = (tier: PricingTier) => {
		if (tier.max_age === null) {
			// Handle case where there's no maximum age
			if (tier.min_age === 0 || tier.min_age === null) {
				return `${tier.tier_name} (pour tous les âges)`;
			}
			return `${tier.tier_name} (+${tier.min_age} ans)`;
		}
		// Handle case where there's both min and max age
		if (tier.min_age === 0 || tier.min_age === null) {
			return `${tier.tier_name} (-${tier.max_age} ans)`;
		}
		return `${tier.tier_name} (${tier.min_age}-${tier.max_age} ans)`;
	};

	const totalParticipants = tierCounts.reduce((sum, tc) => sum + tc.count, 0);
	const totalPrice = fixedPrice ? basePrice : tierCounts.reduce((sum, tc) => sum + tc.count * tc.tier.price, 0);
	const canAddMore = totalParticipants < maxParticipants;

	return (
		<Card className={className}>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Euro className="w-5 h-5" />
					{fixedPrice ? "Prix fixe et Participants" : "Tarifs et Participants"}
				</CardTitle>
				<p className="text-sm text-gray-600">
					{fixedPrice
						? "Sélectionnez le nombre de participants (prix fixe)"
						: "Sélectionnez le nombre de participants par catégorie d'âge"}
				</p>
			</CardHeader>
			<CardContent className="space-y-4">
				{/* Pricing Tiers with Counters */}
				<div className="space-y-3">
					{tierCounts.map(({ tier, count }) => (
						<div
							key={tier.id}
							className="flex items-center justify-between p-4 border rounded-lg bg-gray-50"
						>
							<div className="flex-1">
								<div className="font-medium text-sm">{getTierDisplayName(tier)}</div>
								<div className="text-emerald-600 font-bold text-lg">
									{fixedPrice ? `${basePrice}€ (prix fixe)` : `${tier.price}€`}
								</div>
							</div>

							<div className="flex items-center gap-3">
								<Button
									size="sm"
									variant="outline"
									disabled={count === 0}
									onClick={() => updateTierCount(tier.id, -1)}
									className="h-8 w-8 p-0"
								>
									<Minus className="w-4 h-4" />
								</Button>

								<div className="w-8 text-center font-medium">{count}</div>

								<Button
									size="sm"
									variant="outline"
									disabled={!canAddMore}
									onClick={() => updateTierCount(tier.id, 1)}
									className="h-8 w-8 p-0 border-emerald-200 text-emerald-600 hover:bg-emerald-50"
								>
									<Plus className="w-4 h-4" />
								</Button>
							</div>
						</div>
					))}
				</div>

				{/* Summary */}
				<div className="border-t pt-4 space-y-3">
					<div className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<Users className="w-4 h-4 text-gray-600" />
							<span className="font-medium text-gray-900">Total Participants</span>
						</div>
						<Badge variant="outline" className="text-emerald-600 border-emerald-200">
							{totalParticipants}/{maxParticipants}
						</Badge>
					</div>

					<div className="flex items-center justify-between">
						<div>
							<div className="font-medium text-gray-900">Prix Total</div>
							<div className="text-sm text-gray-600">
								{fixedPrice
									? `Prix fixe pour ${totalParticipants} participant${
											totalParticipants > 1 ? "s" : ""
									  }`
									: `${totalParticipants} participant${totalParticipants > 1 ? "s" : ""}`}
							</div>
						</div>
						<div className="text-2xl font-bold text-emerald-600">{totalPrice}€</div>
					</div>
				</div>

				{/* Capacity Warning */}
				{!canAddMore && totalParticipants > 0 && (
					<div className="text-sm text-amber-600 bg-amber-50 p-3 rounded-lg">
						Capacité maximale atteinte ({maxParticipants} participants)
					</div>
				)}

				{/* No participants warning */}
				{totalParticipants === 0 && (
					<div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
						Ajoutez au moins un participant pour continuer
					</div>
				)}
			</CardContent>
		</Card>
	);
}
